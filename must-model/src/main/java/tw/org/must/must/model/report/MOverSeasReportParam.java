package tw.org.must.must.model.report;

import java.io.Serializable;
import java.util.List;

/**
 * 海外权利金明细表报表参数实体类
 * 整合了 MOverSeas_main.jrxml 和 MOverSeas_sub.jrxml 的所有参数
 * 
 * <AUTHOR> Generated
 */
public class MOverSeasReportParam implements Serializable {

    private static final long serialVersionUID = 1L;

    // ==================== 主报表参数 (Parameters) ====================
    
    /**
     * 子报表目录路径
     */
    private String subreportDir;
    
    /**
     * 海外权利金分配编号
     */
    private String distributionNo;
    
    /**
     * 报表日期
     */
    private String date;
    
    /**
     * MUST会员名称
     */
    private String mustMemberName;
    
    /**
     * IP基础编号
     */
    private String ipBaseNo;
    
    /**
     * PA名称编号
     */
    private String paNameNo;
    
    /**
     * 总作品数
     */
    private String totalWorks;
    
    /**
     * 电视权利金总计
     */
    private String tvTotal;
    
    /**
     * 广播权利金总计
     */
    private String radioTotal;
    
    /**
     * 演唱会权利金总计
     */
    private String concertTotal;
    
    /**
     * 卡拉OK权利金总计
     */
    private String karaokeTotal;
    
    /**
     * 航空权利金总计
     */
    private String airlineTotal;
    
    /**
     * 其他权利金总计
     */
    private String otherTotal;
    
    /**
     * 权利金总计
     */
    private String total;

    // ==================== 主报表数据字段 (Fields) ====================
    
    /**
     * 主报表数据列表
     */
    private List<MOverSeasMainData> mainDataList;

    // ==================== 内部类：主报表数据结构 ====================
    
    /**
     * 主报表数据结构
     */
    public static class MOverSeasMainData implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 作品编号
         */
        private String workNo;
        
        /**
         * 作品标题
         */
        private String workTitle;
        
        /**
         * 原始标题
         */
        private String originaltitle;
        
        /**
         * SD字段
         */
        private String sd;
        
        /**
         * 作品IP权利金列表（子报表数据）
         */
        private List<MOverSeasSubData> workIpRoyList;
        
        /**
         * 总计
         */
        private String total;
        
        /**
         * 状态
         */
        private String status;
        
        /**
         * IP名称编号
         */
        private String ipNameNo;
        
        /**
         * 协会
         */
        private String society;
        
        /**
         * 电视权利金
         */
        private String tv;
        
        /**
         * 广播权利金
         */
        private String radio;
        
        /**
         * 演唱会权利金
         */
        private String concert;
        
        /**
         * 卡拉OK权利金
         */
        private String karaoke;
        
        /**
         * 一般权利金
         */
        private String general;
        
        /**
         * 其他权利金
         */
        private String others;
        
        /**
         * 作品总计
         */
        private String workTotal;

        // Getter and Setter methods
        public String getWorkNo() {
            return workNo;
        }

        public void setWorkNo(String workNo) {
            this.workNo = workNo;
        }

        public String getWorkTitle() {
            return workTitle;
        }

        public void setWorkTitle(String workTitle) {
            this.workTitle = workTitle;
        }

        public String getOriginaltitle() {
            return originaltitle;
        }

        public void setOriginaltitle(String originaltitle) {
            this.originaltitle = originaltitle;
        }

        public String getSd() {
            return sd;
        }

        public void setSd(String sd) {
            this.sd = sd;
        }

        public List<MOverSeasSubData> getWorkIpRoyList() {
            return workIpRoyList;
        }

        public void setWorkIpRoyList(List<MOverSeasSubData> workIpRoyList) {
            this.workIpRoyList = workIpRoyList;
        }

        public String getTotal() {
            return total;
        }

        public void setTotal(String total) {
            this.total = total;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getIpNameNo() {
            return ipNameNo;
        }

        public void setIpNameNo(String ipNameNo) {
            this.ipNameNo = ipNameNo;
        }

        public String getSociety() {
            return society;
        }

        public void setSociety(String society) {
            this.society = society;
        }

        public String getTv() {
            return tv;
        }

        public void setTv(String tv) {
            this.tv = tv;
        }

        public String getRadio() {
            return radio;
        }

        public void setRadio(String radio) {
            this.radio = radio;
        }

        public String getConcert() {
            return concert;
        }

        public void setConcert(String concert) {
            this.concert = concert;
        }

        public String getKaraoke() {
            return karaoke;
        }

        public void setKaraoke(String karaoke) {
            this.karaoke = karaoke;
        }

        public String getGeneral() {
            return general;
        }

        public void setGeneral(String general) {
            this.general = general;
        }

        public String getOthers() {
            return others;
        }

        public void setOthers(String others) {
            this.others = others;
        }

        public String getWorkTotal() {
            return workTotal;
        }

        public void setWorkTotal(String workTotal) {
            this.workTotal = workTotal;
        }
    }

    // ==================== 内部类：子报表数据结构 ====================
    
    /**
     * 子报表数据结构（IP权利金详细信息）
     */
    public static class MOverSeasSubData implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * IP名称
         */
        private String ipName;
        
        /**
         * 状态
         */
        private String status;
        
        /**
         * IP名称编号
         */
        private String ipNameNo;
        
        /**
         * 协会
         */
        private String society;
        
        /**
         * 份额
         */
        private String share;
        
        /**
         * 电视权利金
         */
        private String tv;
        
        /**
         * 广播权利金
         */
        private String radio;
        
        /**
         * 演唱会权利金
         */
        private String concert;
        
        /**
         * 卡拉OK权利金
         */
        private String karaoke;
        
        /**
         * 航空权利金
         */
        private String airline;
        
        /**
         * 其他权利金
         */
        private String others;
        
        /**
         * 总计
         */
        private String total;

        // Getter and Setter methods
        public String getIpName() {
            return ipName;
        }

        public void setIpName(String ipName) {
            this.ipName = ipName;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getIpNameNo() {
            return ipNameNo;
        }

        public void setIpNameNo(String ipNameNo) {
            this.ipNameNo = ipNameNo;
        }

        public String getSociety() {
            return society;
        }

        public void setSociety(String society) {
            this.society = society;
        }

        public String getShare() {
            return share;
        }

        public void setShare(String share) {
            this.share = share;
        }

        public String getTv() {
            return tv;
        }

        public void setTv(String tv) {
            this.tv = tv;
        }

        public String getRadio() {
            return radio;
        }

        public void setRadio(String radio) {
            this.radio = radio;
        }

        public String getConcert() {
            return concert;
        }

        public void setConcert(String concert) {
            this.concert = concert;
        }

        public String getKaraoke() {
            return karaoke;
        }

        public void setKaraoke(String karaoke) {
            this.karaoke = karaoke;
        }

        public String getAirline() {
            return airline;
        }

        public void setAirline(String airline) {
            this.airline = airline;
        }

        public String getOthers() {
            return others;
        }

        public void setOthers(String others) {
            this.others = others;
        }

        public String getTotal() {
            return total;
        }

        public void setTotal(String total) {
            this.total = total;
        }
    }

    // ==================== 主报表参数的 Getter 和 Setter 方法 ====================

    public String getSubreportDir() {
        return subreportDir;
    }

    public void setSubreportDir(String subreportDir) {
        this.subreportDir = subreportDir;
    }

    public String getDistributionNo() {
        return distributionNo;
    }

    public void setDistributionNo(String distributionNo) {
        this.distributionNo = distributionNo;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getMustMemberName() {
        return mustMemberName;
    }

    public void setMustMemberName(String mustMemberName) {
        this.mustMemberName = mustMemberName;
    }

    public String getIpBaseNo() {
        return ipBaseNo;
    }

    public void setIpBaseNo(String ipBaseNo) {
        this.ipBaseNo = ipBaseNo;
    }

    public String getPaNameNo() {
        return paNameNo;
    }

    public void setPaNameNo(String paNameNo) {
        this.paNameNo = paNameNo;
    }

    public String getTotalWorks() {
        return totalWorks;
    }

    public void setTotalWorks(String totalWorks) {
        this.totalWorks = totalWorks;
    }

    public String getTvTotal() {
        return tvTotal;
    }

    public void setTvTotal(String tvTotal) {
        this.tvTotal = tvTotal;
    }

    public String getRadioTotal() {
        return radioTotal;
    }

    public void setRadioTotal(String radioTotal) {
        this.radioTotal = radioTotal;
    }

    public String getConcertTotal() {
        return concertTotal;
    }

    public void setConcertTotal(String concertTotal) {
        this.concertTotal = concertTotal;
    }

    public String getKaraokeTotal() {
        return karaokeTotal;
    }

    public void setKaraokeTotal(String karaokeTotal) {
        this.karaokeTotal = karaokeTotal;
    }

    public String getAirlineTotal() {
        return airlineTotal;
    }

    public void setAirlineTotal(String airlineTotal) {
        this.airlineTotal = airlineTotal;
    }

    public String getOtherTotal() {
        return otherTotal;
    }

    public void setOtherTotal(String otherTotal) {
        this.otherTotal = otherTotal;
    }

    public String getTotal() {
        return total;
    }

    public void setTotal(String total) {
        this.total = total;
    }

    public List<MOverSeasMainData> getMainDataList() {
        return mainDataList;
    }

    public void setMainDataList(List<MOverSeasMainData> mainDataList) {
        this.mainDataList = mainDataList;
    }
}
