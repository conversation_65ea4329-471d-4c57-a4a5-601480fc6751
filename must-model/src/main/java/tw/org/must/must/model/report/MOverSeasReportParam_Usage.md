# MOverSeasReportParam 使用说明

## 概述

`MOverSeasReportParam` 是海外权利金明细表报表的参数实体类，整合了 `MOverSeas_main.jrxml` 和 `MOverSeas_sub.jrxml` 两个JasperReports模板文件的所有参数和数据字段。

## 类结构

### 主要组成部分

1. **主报表参数 (Parameters)** - 对应 MOverSeas_main.jrxml 的 `<parameter>` 标签
2. **主报表数据 (MOverSeasMainData)** - 对应 MOverSeas_main.jrxml 的 `<field>` 标签
3. **子报表数据 (MOverSeasSubData)** - 对应 MOverSeas_sub.jrxml 的 `<field>` 标签

### 内部类说明

- `MOverSeasMainData`: 主报表的数据结构，包含作品信息和权利金汇总
- `MOverSeasSubData`: 子报表的数据结构，包含IP详细信息和各类型权利金明细

## 使用示例

```java
// 创建报表参数对象
MOverSeasReportParam reportParam = new MOverSeasReportParam();

// 设置主报表参数
reportParam.setSubreportDir("ireport/jrxml/royaltyDetails/");
reportParam.setDistributionNo("O221");
reportParam.setDate("2024-01-15");
reportParam.setMustMemberName("示例会员名称");
reportParam.setIpBaseNo("IP001");
reportParam.setPaNameNo("PA001");
reportParam.setTotalWorks("100");
reportParam.setTvTotal("50000.00");
reportParam.setRadioTotal("30000.00");
reportParam.setConcertTotal("20000.00");
reportParam.setKaraokeTotal("15000.00");
reportParam.setAirlineTotal("10000.00");
reportParam.setOtherTotal("5000.00");
reportParam.setTotal("130000.00");

// 创建主报表数据列表
List<MOverSeasReportParam.MOverSeasMainData> mainDataList = new ArrayList<>();

// 创建主报表数据项
MOverSeasReportParam.MOverSeasMainData mainData = new MOverSeasReportParam.MOverSeasMainData();
mainData.setWorkNo("W001");
mainData.setWorkTitle("示例作品标题");
mainData.setOriginaltitle("Original Title");
mainData.setSd("SD001");
mainData.setStatus("A");
mainData.setIpNameNo("IP001");
mainData.setSociety("MUST");
mainData.setTv("1000.00");
mainData.setRadio("800.00");
mainData.setConcert("600.00");
mainData.setKaraoke("400.00");
mainData.setGeneral("300.00");
mainData.setOthers("200.00");
mainData.setWorkTotal("3300.00");

// 创建子报表数据列表
List<MOverSeasReportParam.MOverSeasSubData> subDataList = new ArrayList<>();

// 创建子报表数据项
MOverSeasReportParam.MOverSeasSubData subData = new MOverSeasReportParam.MOverSeasSubData();
subData.setIpName("示例IP名称");
subData.setStatus("CA");
subData.setIpNameNo("IP001");
subData.setSociety("MUST");
subData.setShare("50.00%");
subData.setTv("500.00");
subData.setRadio("400.00");
subData.setConcert("300.00");
subData.setKaraoke("200.00");
subData.setAirline("150.00");
subData.setOthers("100.00");
subData.setTotal("1650.00");

subDataList.add(subData);
mainData.setWorkIpRoyList(subDataList);

mainDataList.add(mainData);
reportParam.setMainDataList(mainDataList);
```

## 字段说明

### 主报表参数字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| subreportDir | String | 子报表目录路径 |
| distributionNo | String | 海外权利金分配编号 |
| date | String | 报表日期 |
| mustMemberName | String | MUST会员名称 |
| ipBaseNo | String | IP基础编号 |
| paNameNo | String | PA名称编号 |
| totalWorks | String | 总作品数 |
| tvTotal | String | 电视权利金总计 |
| radioTotal | String | 广播权利金总计 |
| concertTotal | String | 演唱会权利金总计 |
| karaokeTotal | String | 卡拉OK权利金总计 |
| airlineTotal | String | 航空权利金总计 |
| otherTotal | String | 其他权利金总计 |
| total | String | 权利金总计 |

### 主报表数据字段 (MOverSeasMainData)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| workNo | String | 作品编号 |
| workTitle | String | 作品标题 |
| originaltitle | String | 原始标题 |
| sd | String | SD字段 |
| workIpRoyList | List&lt;MOverSeasSubData&gt; | 作品IP权利金列表 |
| total | String | 总计 |
| status | String | 状态 |
| ipNameNo | String | IP名称编号 |
| society | String | 协会 |
| tv | String | 电视权利金 |
| radio | String | 广播权利金 |
| concert | String | 演唱会权利金 |
| karaoke | String | 卡拉OK权利金 |
| general | String | 一般权利金 |
| others | String | 其他权利金 |
| workTotal | String | 作品总计 |

### 子报表数据字段 (MOverSeasSubData)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| ipName | String | IP名称 |
| status | String | 状态 |
| ipNameNo | String | IP名称编号 |
| society | String | 协会 |
| share | String | 份额 |
| tv | String | 电视权利金 |
| radio | String | 广播权利金 |
| concert | String | 演唱会权利金 |
| karaoke | String | 卡拉OK权利金 |
| airline | String | 航空权利金 |
| others | String | 其他权利金 |
| total | String | 总计 |

## 注意事项

1. 所有金额字段使用 String 类型，与原始 JRXML 模板保持一致
2. 子报表数据通过 `workIpRoyList` 字段关联到主报表数据
3. 该实体类设计为与 JasperReports 模板完全兼容
4. 所有类都实现了 `Serializable` 接口，支持序列化操作

## 与 JasperReports 集成

在使用 JasperReports 生成报表时，可以将此实体类转换为相应的参数 Map 和数据源：

```java
// 转换为 JasperReports 参数
Map<String, Object> parameters = new HashMap<>();
parameters.put("SUBREPORT_DIR", reportParam.getSubreportDir());
parameters.put("distributionNo", reportParam.getDistributionNo());
parameters.put("date", reportParam.getDate());
// ... 其他参数

// 转换为 JSON 数据源
String jsonData = objectMapper.writeValueAsString(reportParam.getMainDataList());
JRDataSource dataSource = new JsonDataSource(new ByteArrayInputStream(jsonData.getBytes()));
```
